using Admin.Business;
using Admin.Components;
using Admin.Controllers;
using Admin.Reporting;
using Admin.UI;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.OpenApi.Models;
using Serilog;
using Syncfusion.Blazor;
using Syncfusion.Blazor.Popups;
using System.Globalization;

namespace Admin;

public class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        builder.Services.AddCors(options =>
        {
            options.AddPolicy("MyOrigins",
                policy =>
                {
                    policy.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod();
                });
        });

        //Configuration
        string appsettingsEnvironmentFile = string.Empty;
        //#if DEBUG
        if (builder.Environment.IsDevelopment())
        {
            appsettingsEnvironmentFile = "appsettings.Development.json";
        }
        //#elif RELEASE
        else if (builder.Environment.IsProduction())
        {
            appsettingsEnvironmentFile = "appsettings.Production.json";
        }
        //#endif
        builder.Configuration.SetBasePath(System.AppContext.BaseDirectory).AddJsonFile("appsettings.json").AddJsonFile(appsettingsEnvironmentFile);


        //Logging
        builder.Host.UseSerilog((context, configuration) => configuration.ReadFrom.Configuration(context.Configuration));

        // Add services to the container.
        builder.Services.AddRazorComponents()
            .AddInteractiveServerComponents();

        //Localization
        builder.Services.AddLocalization();
        var supportedCultures = new[] { "el-GR", "en-US" };
        var localizationOptions = new RequestLocalizationOptions()
            .SetDefaultCulture(supportedCultures[0])
            .AddSupportedCultures(supportedCultures)
            .AddSupportedUICultures(supportedCultures);
    
        //Syncfusion
        Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("NRAiBiAaIQQuGjN/V05+XU9HdVRDX3xKf0x/TGpQb19xflBPallYVBYiSV9jS3tTdERlWH9bcXBdTmRUWQ==;MzcyMjA3N0AzMjM4MmUzMDJlMzBJZ2xRdDVMVFBuUFZXckhIM05sMVVNcGFERFNVUlFxTlZ2amtDVjFtUXJ3PQ==");
        builder.Services.AddSyncfusionBlazor();
        builder.Services.AddSingleton(typeof(ISyncfusionStringLocalizer), typeof(SyncfusionLocalizer));   // Register the locale service to localize the  SyncfusionBlazor components.


        // Add services to the container.

        builder.Services.AddControllers();
        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "MentalView.Admin.WebApi", Version = "v1" });
        });

        #region  Dependency Injection
        builder.Services.AddTransient<SfDialogService>();
        builder.Services.AddScoped<ITenantsBusiness>(sp => new Admin.Business.TenantsBusiness(new Data.Model.MentalViewContext(builder.Configuration["MentalViewConnectionString"]!), sp.GetRequiredService<IConfiguration>(), sp.GetRequiredService<ILogger<TenantsBusiness>>()));
        builder.Services.AddScoped<IRolesBusiness>(sp => new Admin.Business.RolesBusiness(new Data.Model.MentalViewContext(builder.Configuration["MentalViewConnectionString"]!), sp.GetRequiredService<IConfiguration>(), sp.GetRequiredService<ILogger<RolesBusiness>>()));
        builder.Services.AddScoped<IUsersBusiness>(sp => new Admin.Business.UsersBusiness(new Data.Model.MentalViewContext(builder.Configuration["MentalViewConnectionString"]!), sp.GetRequiredService<IConfiguration>(), sp.GetRequiredService<ILogger<UsersBusiness>>()));
        builder.Services.AddScoped<IAppointmentCategoriesBusiness>(sp => new Admin.Business.AppointmentCategoriesBusiness(new Data.Model.MentalViewContext(builder.Configuration["MentalViewConnectionString"]!), sp.GetRequiredService<IConfiguration>(), sp.GetRequiredService<ILogger<AppointmentCategoriesBusiness>>()));
        builder.Services.AddScoped<IReportsFactory, ReportsFactory>(); // Replace ReportsImplementation with the actual implementation class

        //builder.Services.AddScoped<ILogger<TenantsController>>(sp =>
        //{

        //    return new Logger<TenantsController>();
        //});
        builder.Services.AddScoped<ILogger<TenantsController>>(sp =>
        {
            var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
            return new Logger<TenantsController>(loggerFactory);
        });

        #endregion

        var app = builder.Build();
        //Localization
        app.UseRequestLocalization(localizationOptions);

        //Configuration
        builder.Configuration.SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json", optional: false, true).AddJsonFile($"appsettings.{app.Environment.EnvironmentName}.json", optional: true, true);



        // Configure the HTTP request pipeline.
        if (!app.Environment.IsDevelopment())
        {
            app.UseExceptionHandler("/Error");
            // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            app.UseHsts();
        }

        app.UseHttpsRedirection();

        app.UseCors(cors => cors
             .AllowAnyMethod()
             .AllowAnyHeader()
             .SetIsOriginAllowed(origin => true)
             .AllowCredentials()
           );
        //app.UseCors("MyCorsPolicy");


        // Configure the HTTP request pipeline.
        if (app.Environment.IsDevelopment() || app.Environment.IsProduction())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseAntiforgery();

        app.MapControllers();

        app.MapStaticAssets();
       
        app.MapRazorComponents<App>()
            .AddInteractiveServerRenderMode();


        CultureInfo cultureInfo = new CultureInfo("el-GR");
        CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
        CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;

        app.Run();
    }
}
